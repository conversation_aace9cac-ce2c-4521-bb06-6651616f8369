import { University } from '../../types/university'
import { But<PERSON> } from '../ui/button'
import { Card } from '../ui/card'
import { cn } from '../../lib/utils'
import {
  ArrowLeft,
  MapPin,
  Calendar,
  GraduationCap,
  Star,
  TrendingUp,
  ExternalLink,
  Phone,
  Globe,
  Users,
  BookOpen,
  Award,
  Building,
  Mail,
  School,
  Trophy,
  Target,
  Download,
  Heart,
  Share2
} from 'lucide-react'

interface UniversityDetailProps {
  university: University
  onBack?: () => void
  className?: string
}

export function UniversityDetail({ university, onBack, className }: UniversityDetailProps) {
  // 安全的数组检查函数
  const safeArray = (arr: any): any[] => {
    return Array.isArray(arr) ? arr : []
  }

  // 获取院校类型标签
  const getTypeLabels = () => {
    const labels = []
    if (university.Is985) labels.push({ text: '985', color: 'bg-red-100 text-red-800' })
    if (university.Is211) labels.push({ text: '211', color: 'bg-orange-100 text-orange-800' })
    if (university.IsDualClass) labels.push({ text: '双一流', color: 'bg-purple-100 text-purple-800' })
    return labels
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      '综合类': 'bg-green-100 text-green-800',
      '理工类': 'bg-blue-100 text-blue-800',
      '师范类': 'bg-yellow-100 text-yellow-800',
      '医药类': 'bg-red-100 text-red-800',
      '财经类': 'bg-purple-100 text-purple-800',
      '艺术类': 'bg-pink-100 text-pink-800',
      '农林类': 'bg-emerald-100 text-emerald-800',
      '政法类': 'bg-indigo-100 text-indigo-800',
      '体育类': 'bg-orange-100 text-orange-800',
      '民族类': 'bg-cyan-100 text-cyan-800',
      '军事类': 'bg-slate-100 text-slate-800',
      '语言类': 'bg-teal-100 text-teal-800',
      '其它': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || colors['其它']
  }

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  返回
                </Button>
              )}
              <div className="flex items-center gap-2">
                <Building className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">{university.CollegeName}</h1>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* 大学头部横幅 */}
        <Card className="p-8 mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                {university.CoverImage && (
                  <img
                    src={university.CoverImage}
                    alt={`${university.CollegeName}校徽`}
                    className="w-16 h-16 rounded-full object-cover border-2 border-white shadow-md"
                  />
                )}
                <div>
                  <h1 className="text-4xl font-bold text-gray-900">{university.CollegeName}</h1>
                  {university.ShortName && university.ShortName !== university.CollegeName && (
                    <p className="text-lg text-gray-600 mt-1">简称：{university.ShortName}</p>
                  )}
                </div>
                {university.Ranking && university.Ranking > 0 && (
                  <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium border border-yellow-200">
                    <Trophy className="w-4 h-4" />
                    全国第{university.Ranking}名
                  </div>
                )}
              </div>

              {/* 标签 */}
              <div className="flex flex-wrap gap-3 mb-6">
                {getTypeLabels().map((label, index) => (
                  <span key={index} className={cn("px-3 py-1 rounded-full text-sm font-medium", label.color)}>
                    {label.text}
                  </span>
                ))}
                <span className={cn("px-3 py-1 rounded-full text-sm font-medium", getCategoryColor(university.CollegeCategory))}>
                  {university.CollegeCategory}
                </span>
                <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                  {university.CollegeProperty}
                </span>
                <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-700">
                  {university.EduLevel}
                </span>
              </div>
            </div>

            {/* 排名信息 */}
            {university.RankingInCategory && (
              <div className="text-center bg-white p-6 rounded-lg shadow-sm border border-blue-200">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {university.RankingInCategory}
                </div>
                <div className="text-sm text-gray-600">类别排名</div>
              </div>
            )}
          </div>

          {/* 基本信息网格 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
            <div className="flex items-center gap-3">
              <MapPin className="w-5 h-5 text-gray-500" />
              <div>
                <div className="text-sm text-gray-500">所在地区</div>
                <div className="font-medium">{university.Province} · {university.City}</div>
                {university.District && (
                  <div className="text-xs text-gray-500">{university.District}</div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Award className="w-5 h-5 text-gray-500" />
              <div>
                <div className="text-sm text-gray-500">院校代码</div>
                <div className="font-medium">{university.CollegeCode || '未知'}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <GraduationCap className="w-5 h-5 text-gray-500" />
              <div>
                <div className="text-sm text-gray-500">办学层次</div>
                <div className="font-medium">{university.EduLevel}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <School className="w-5 h-5 text-gray-500" />
              <div>
                <div className="text-sm text-gray-500">院校类型</div>
                <div className="font-medium">{university.CollegeType}</div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-4 pt-6 border-t border-blue-200">
            {university.WebSite && (
              <Button
                className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                onClick={() => window.open(university.WebSite, '_blank')}
              >
                <Globe className="w-4 h-4" />
                访问官网
                <ExternalLink className="w-3 h-3" />
              </Button>
            )}
            {university.CallNumber && (
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => window.open(`tel:${university.CallNumber}`, '_self')}
              >
                <Phone className="w-4 h-4" />
                联系电话
              </Button>
            )}
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              下载招生简章
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              收藏
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Share2 className="w-4 h-4" />
              分享
            </Button>
          </div>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 学校简介 */}
            {university.Intro && (
              <Card className="p-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                  学校简介
                </h2>
                <div className="prose prose-gray max-w-none">
                  <p className="text-gray-700 leading-relaxed text-base">{university.Intro}</p>
                </div>
              </Card>
            )}

            {/* 开设专业 */}
            {(() => {
              const majorList = safeArray(university.MajorList)
              return majorList.length > 0 && (
                <Card className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <Award className="w-6 h-6 text-green-600" />
                    开设专业
                  </h2>
                  <div className="space-y-4">
                    {majorList.map((majorGroup, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-bold text-lg text-gray-800 mb-3">
                          {majorGroup?.MajorTitle || '未知专业类别'}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {safeArray(majorGroup?.Majors).map((major, majorIndex) => (
                            <div
                              key={majorIndex}
                              className="p-2 bg-green-50 text-green-800 rounded text-sm border border-green-200 hover:bg-green-100 transition-colors"
                            >
                              {major}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              )
            })()}
          </div>

          {/* 右侧信息 */}
          <div className="space-y-6">
            {/* 学校特色 */}
            {(() => {
              const tags = safeArray(university.CollegeTags)
              return tags.length > 0 && (
                <Card className="p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">学校特色</h3>
                  <div className="space-y-2">
                    {tags.map((tag, index) => (
                      <div
                        key={index}
                        className="p-3 bg-blue-50 text-blue-800 rounded-lg text-sm border border-blue-200"
                      >
                        {tag}
                      </div>
                    ))}
                  </div>
                </Card>
              )
            })()}

            {/* 排名信息 */}
            {(university.Ranking || university.RankingInCategory) && (
              <Card className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-orange-600" />
                  排名信息
                </h3>
                <div className="space-y-3">
                  {university.Ranking && university.Ranking > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">全国排名</span>
                      <span className="font-bold text-blue-600">第{university.Ranking}名</span>
                    </div>
                  )}
                  {university.RankingInCategory && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">类别排名</span>
                      <span className="font-bold text-green-600">{university.RankingInCategory}</span>
                    </div>
                  )}
                </div>
              </Card>
            )}

            {/* 学费信息 */}
            {university.Expenses && (
              <Card className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Target className="w-5 h-5 text-purple-600" />
                  学费信息
                </h3>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 mb-2">
                    {university.Expenses}
                  </div>
                  <div className="text-sm text-gray-600">年学费标准</div>
                </div>
              </Card>
            )}

            {/* 联系信息 */}
            <Card className="p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">联系信息</h3>
              <div className="space-y-3 text-sm">
                {university.Address && (
                  <div>
                    <span className="text-gray-600">学校地址：</span>
                    <span className="text-gray-900">{university.Address}</span>
                  </div>
                )}
                {university.CallNumber && (
                  <div>
                    <span className="text-gray-600">联系电话：</span>
                    <span className="text-gray-900">{university.CallNumber}</span>
                  </div>
                )}
                {university.Email && (
                  <div>
                    <span className="text-gray-600">邮箱地址：</span>
                    <a
                      href={`mailto:${university.Email}`}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      {university.Email}
                    </a>
                  </div>
                )}
                {university.WebSite && (
                  <div>
                    <span className="text-gray-600">官方网站：</span>
                    <a
                      href={university.WebSite}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-700 flex items-center gap-1"
                    >
                      访问官网
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </div>
                )}
              </div>
            </Card>

            {/* 快速操作 */}
            <Card className="p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">快速操作</h3>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Target className="w-4 h-4 mr-2" />
                  查看录取概率
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Users className="w-4 h-4 mr-2" />
                  对比其他院校
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <BookOpen className="w-4 h-4 mr-2" />
                  查看专业详情
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  下载资料
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
